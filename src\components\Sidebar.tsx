import { useNavigate } from 'react-router-dom'
import React, { useState, useEffect } from 'react'
import './icons/styles.css'
import { DifyApi, IConversationItem } from '../api/src/dify-api'
import { XAiApi, IGetAiAppInfoResponse } from '../api/src/xai-api'
import Cookies from 'js-cookie'
import { useI18nRouter, useSimpleTranslation } from '../i18n/simple-hooks'
import { HistoryIcon } from './icons/Icons'

interface ApiProps {
  conversationList: IConversationItem[]
  onConversationClick: () => void
  appUuid: string
}

// 简化的历史提问列表组件（适用于侧边栏）
const SidebarConversationList: React.FC<ApiProps> = ({ conversationList, onConversationClick, appUuid }) => {
  const navigate = useNavigate()
  const { navigateToApp } = useI18nRouter()
  const { t } = useSimpleTranslation()

  const handleConversationClick = (conversationId: string) => {
    // 使用新的国际化路由结构
    if (appUuid) {
      navigateToApp(appUuid, conversationId)
    }
  }

  // 限制显示最多10个对话，取最新的10个
  const displayedConversations = conversationList.slice(0, 10);
  const hasMoreConversations = conversationList.length > 10;

  return (
    <div className="relative">
      <ul className="relative pl-0 list-none m-0">
        {displayedConversations.length > 0 ? (
          displayedConversations.map((conversation, index) => {
            const isLast = index === displayedConversations.length - 1;
            return (
              <li
                key={conversation.id}
                onClick={() => handleConversationClick(conversation.id)}
                className="group cursor-pointer relative py-1 text-sm text-gray-500 hover:text-blue-600 rounded-md mx-1 px-2 transition-all duration-200"
              >
                {/* 垂直连接线 - 对于最后一个项目，只显示到中间位置 */}
                {!isLast && (
                  <div
                    className="absolute left-[8px] top-[50%] bottom-0"
                    style={{
                      borderLeft: '1px solid #d1d5db'
                    }}
                  ></div>
                )}
                {/* 从顶部到中间的连接线 - 最后项目且有圆角时不显示 */}
                {!isLast && (
                  <div
                    className="absolute left-[8px] top-0 h-[50%]"
                    style={{
                      borderLeft: '1px solid #d1d5db'
                    }}
                  ></div>
                )}

                {/* 对于最后一个项目，添加圆角连接 */}
                {isLast ? (
                  <div
                    className="absolute left-[8px] top-0"
                    style={{
                      width: '12px',
                      height: '50%',
                      borderLeft: '1px solid #d1d5db',
                      borderBottom: '1px solid #d1d5db',
                      borderBottomLeftRadius: '6px',
                    }}
                  ></div>
                ) : (
                  /* 水平连接线 - 只对非最后项目显示 */
                  <div
                    className="absolute left-[8px] top-[50%] w-3 transform -translate-y-1/2"
                    style={{
                      borderBottom: '1px solid #d1d5db'
                    }}
                  ></div>
                )}

                <div className="pl-6">
                  <span className="tree-item-text block truncate group-hover:font-medium transition-all duration-200" title={conversation.name}>{conversation.name}</span>
                </div>
              </li>
            );
          })

        ) : (
          <li className="relative py-2 text-sm text-gray-500 pl-6">
            {t('sidebar.noHistory')}
          </li>
        )}
        {conversationList.length > 0 && (
          <li
            onClick={() => onConversationClick()}
            className="relative py-2 text-sm text-gray-500 pl-6 cursor-pointer hover:text-blue-600 rounded-lg mx-2 transition-all duration-200 group"
          >
            <div className="flex items-center">
              <HistoryIcon
                width={14}
                height={14}
                className="mr-2 group-hover:text-blue-600 transition-colors duration-200"
                aria-label="查看历史记录"
              />
              <span className="group-hover:underline">
                {t('sidebar.viewAllHistory')}
              </span>
            </div>
          </li>
        )}
      </ul>
    </div>
  )
}

interface SidebarProps {
  onSearchClick: () => void
  onClose?: () => void
  onConversationClick: () => void
  difyApi: DifyApi
  currentApp: IGetAiAppInfoResponse
}

const Sidebar = ({ onSearchClick, onClose, onConversationClick, difyApi, currentApp }: SidebarProps) => {
  const navigate = useNavigate()
  const { t } = useSimpleTranslation()
  const [appConversations, setAppConversations] = useState<Record<string, IConversationItem[]>>({})

  // 获取指定app的conversation列表
  const fetchAppConversations = async (dAppUuid: string) => {
    const yudaoToken = Cookies.get('yudaoToken')
    if (!yudaoToken) {
      return
    }
    try {
      // 创建临时的difyApi实例来获取该app的conversations
      const tempDifyApi = new DifyApi({
        user: difyApi.options.user,
        apiBase: difyApi.options.apiBase,
        yudaoToken: difyApi.options.yudaoToken,
        appId: dAppUuid,
      })

      const response = await tempDifyApi.getConversationList()
      setAppConversations(prev => ({
        // ...prev,
        [dAppUuid]: response.data
      }))
    } catch (error) {
      setAppConversations(prev => ({
        ...prev,
        [dAppUuid]: []
      }))
    }
  }

  // 在页面加载时获取所有应用的conversations
  useEffect(() => {
    fetchAppConversations(currentApp?.dAppUuid)
  }, [currentApp]) // 依赖appList和difyApi，当它们变化时重新获取

  const handleNavClick = (appUuid: string, dAppUuid: string) => {
    if (dAppUuid) {
      // 如果是展开操作且还没有获取过该app的对话列表，则获取
      if (!appConversations[dAppUuid]) {
        fetchAppConversations(dAppUuid)
      }
      // 打开会话历史弹框而不是导航
      onConversationClick()
      return
    }

    // 在移动端点击后关闭侧边栏
    if (onClose) {
      onClose()
    }
  }

  // 处理点击 Logo 回到首页
  const handleLogoClick = () => {
    window.location.href = '/'
  }

  return (
    <div
      className="w-60 h-full flex flex-col p-4 slide-in bg-white shadow-xl"
      style={{ backgroundColor: 'var(--bg-sidebar)' }}
    >
      {/* 移动端关闭按钮 */}
      <div className="flex justify-between items-center mb-6 md:hidden">
        <h2 className="text-lg font-semibold text-gray-900">菜单</h2>
        {onClose && (
          <button
            onClick={onClose}
            className="p-2 text-gray-500 hover:text-gray-700 rounded-lg transition-colors"
          >
            ✕
          </button>
        )}
      </div>

      {/* 左侧Logo区域 */}
      <div className="flex items-center gap-2 md:gap-3 mb-6">
        {/* Logo - 可点击回到首页 */}
        <button
          onClick={handleLogoClick}
          className="flex items-center gap-3 hover:opacity-80 transition-opacity cursor-pointer"
        >
          <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-blue-500 via-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
            <span className="text-white font-bold text-lg md:text-xl">xAI</span>
          </div>
          <span className="font-bold text-gray-800 text-lg sm:text-xl md:text-2xl tracking-tight">MedSci xAi</span>
        </button>
      </div>

      {/* 搜索框 */}
      <button
        style={{ display: 'none' }}
        onClick={onSearchClick}
        className="flex items-center gap-3 w-full p-3 bg-white rounded-xl text-gray-600 hover:bg-gray-50 transition-colors mb-6 btn-hover shadow-sm border border-gray-200"
      >
        <div className="nav-icon">
          <span>🔍</span>
        </div>
        <span className="text-sm font-medium">{t('sidebar.search')}</span>
        <span className="ml-auto text-xs bg-gray-100 px-2 py-1 rounded-lg">⌘+K</span>
      </button>

      {/* 导航菜单 */}
      <div className="flex-1 mb-6">
        <div>
          <div className={"nav-item active"}>
            <div className={"nav-icon"}>
              <span>🏠</span>
            </div>
            <span >{t('sidebar.home')}</span>
          </div>
        </div>

        {/* 应用列表 */}
        <div className="relative">
            <div className="relative">
              <div
                onClick={() => handleNavClick(currentApp.appUuid, currentApp.dAppUuid)}
                className="flex items-center gap-3 py-2 px-3 cursor-pointer mb-0 text-gray-700 rounded-lg transition-all duration-200 relative group hover:scale-[1.02]"
              >
                <div className="flex items-center justify-center w-6 h-6">
                  <img src={currentApp.appIcon} alt={currentApp.appName} className="w-6 h-6" />
                </div>
                <span className="font-semibold text-sm text-gray-700 group-hover:text-blue-700 truncate flex-1 transition-colors duration-200" title={currentApp.appName}>
                  {currentApp.appName}
                </span>
              </div>

              <div className="ml-2 mt-1 pl-2">
                <SidebarConversationList
                  conversationList={appConversations[currentApp.dAppUuid] || []}
                  onConversationClick={onConversationClick}
                  appUuid={currentApp.appNameEn}
                />
              </div>
            </div>
            
        </div>
      </div>

      {/* 底部版权信息 */}
      <div className="text-xs leading-5 mt-auto border-t border-gray-200 pt-4">
        {/* 上行：公司信息 */}
        <div className="mb-2 font-medium" style={{ color: '#A8AEB9' }}>
          {t('footer.copyright')}
        </div>
        {/* 下行：备案信息 */}
        <div className="cursor-pointer transition-colors">
          <a
            href="https://beian.miit.gov.cn/"
            target="_blank"
            rel="noopener noreferrer"
            className="hover:text-blue-600 hover:underline transition-all duration-200 font-medium"
            style={{ color: '#D7DBE0' }}
          >
            {t('footer.icpLicense')}
          </a>
        </div>
      </div>
    </div>
  )
}

export default Sidebar